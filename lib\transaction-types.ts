"use client"

import { z } from "zod"

/**
 * Enum for transaction sources
 */
export enum TransactionSource {
  POS = "pos",
  CALENDAR = "calendar",
  MANUAL = "manual",
  INVENTORY = "inventory",
  ONLINE = "online",
  SYSTEM = "system",
  CLIENT_PORTAL = "client_portal"
}

/**
 * Get user-friendly label for transaction source
 */
export function getTransactionSourceLabel(source: TransactionSource): string {
  switch (source) {
    case TransactionSource.POS:
      return "POS"
    case TransactionSource.CALENDAR:
      return "Walk-in"
    case TransactionSource.MANUAL:
      return "Manual Entry"
    case TransactionSource.INVENTORY:
      return "Inventory"
    case TransactionSource.ONLINE:
      return "Online Store"
    case TransactionSource.CLIENT_PORTAL:
      return "Client Portal"
    default:
      return "System"
  }
}

/**
 * Enum for transaction types
 */
export enum TransactionType {
  INCOME = "income",
  EXPENSE = "expense",
  REFUND = "refund",
  ADJUSTMENT = "adjustment",
  INVENTORY_PURCHASE = "inventory_purchase",
  INVENTORY_SALE = "inventory_sale",
  PRODUCT_SALE = "product_sale",
  SERVICE_SALE = "service_sale",
  CONSOLIDATED_SALE = "consolidated_sale", // For transactions with both services and products
  PAYMENT = "payment",
  COGS = "cogs", // Cost of Goods Sold
  GIFT_CARD_SALE = "gift_card_sale",
  GIFT_CARD_REDEMPTION = "gift_card_redemption",
  MEMBERSHIP_SALE = "membership_sale",
  MEMBERSHIP_RENEWAL = "membership_renewal"
}

/**
 * Enum for transaction status
 */
export enum TransactionStatus {
  COMPLETED = "completed",
  PENDING = "pending",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
  PARTIAL = "partial"
}

/**
 * Enum for payment methods
 */
export enum PaymentMethod {
  CREDIT_CARD = "credit_card",
  CASH = "cash",
  BANK_TRANSFER = "bank_transfer",
  MOBILE_PAYMENT = "mobile_payment",
  CHECK = "check",
  GIFT_CARD = "gift_card",
  LOYALTY_POINTS = "loyalty_points",
  OTHER = "other"
}

/**
 * Interface for transaction items (products/services in a transaction)
 */
export interface TransactionItem {
  id: string;
  name: string;
  category: string;
  amount: number;
  quantity?: number;
  serviceId?: string;
  productId?: string;
  description?: string;
  discountAmount?: number;
  discountPercentage?: number;
  originalAmount?: number;
}

/**
 * Interface for transaction data
 */
interface TransactionMetadata {
  discountApplied?: boolean;
  discountPercentage?: number;
  discountAmount?: number;
  originalTotal?: number;
  appointmentId?: string;
  bookingReference?: string;
  completedAt?: string | Date;
  isOnlineTransaction?: boolean;
  serviceId?: string;
  serviceIds?: string[];
  serviceName?: string;
  serviceNames?: string[];
  [key: string]: any; // Allow additional string-indexed properties
}

export interface Transaction {
  id: string;
  date: Date | string;
  clientId?: string;
  clientName?: string;
  staffId?: string;
  staffName?: string;
  type: TransactionType;
  category: string;
  description: string;
  amount: number;
  paymentMethod: PaymentMethod;
  status: TransactionStatus;
  location: string;
  source: TransactionSource;
  reference?: {
    type: string;
    id: string;
  };
  metadata?: TransactionMetadata;
  items?: TransactionItem[];
  serviceAmount?: number;
  productAmount?: number;
  originalServiceAmount?: number;
  discountPercentage?: number;
  discountAmount?: number;
  productId?: string;
  productName?: string;
  quantity?: number;
  costPrice?: number;
  retailPrice?: number;
  profitMargin?: number;
  inventoryTransactionId?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

/**
 * Zod schema for transaction validation
 */
export const transactionSchema = z.object({
  id: z.string().optional(),
  date: z.union([z.date(), z.string().datetime()]),
  clientId: z.string().optional(),
  clientName: z.string().optional(),
  staffId: z.string().optional(),
  staffName: z.string().optional(),
  type: z.nativeEnum(TransactionType),
  category: z.string(),
  description: z.string().optional(),
  amount: z.number().positive(),
  paymentMethod: z.nativeEnum(PaymentMethod),
  status: z.nativeEnum(TransactionStatus),
  location: z.string(),
  source: z.nativeEnum(TransactionSource),
  reference: z.object({
    type: z.string(),
    id: z.string()
  }).optional(),
  metadata: z.record(z.any()).optional(),
  createdAt: z.union([z.date(), z.string().datetime()]).optional(),
  updatedAt: z.union([z.date(), z.string().datetime()]).optional()
});

/**
 * Type for transaction creation
 */
export type TransactionCreate = z.infer<typeof transactionSchema>;

/**
 * Interface for transaction filter options
 */
export interface TransactionFilter {
  startDate?: Date;
  endDate?: Date;
  singleDate?: Date;
  type?: TransactionType;
  source?: TransactionSource | 'all';
  status?: TransactionStatus;
  location?: string;
  clientId?: string;
  staffId?: string;
  search?: string;
  minAmount?: number;
  maxAmount?: number;
}
