/**
 * End-to-end test to verify the complete discount flow from payment dialog to transaction recording
 */

// Mock the ConsolidatedTransactionService.createPOSTransaction method
const mockCreatePOSTransaction = (posData, paymentMethod, discountPercentage, discountAmount) => {
  console.log('\n📋 ConsolidatedTransactionService.createPOSTransaction called with:');
  console.log(`- Payment Method: ${paymentMethod}`);
  console.log(`- Discount Percentage: ${discountPercentage || 'N/A'}`);
  console.log(`- Discount Amount: ${discountAmount || 'N/A'}`);
  console.log(`- Cart Items: ${posData.items.length} items`);

  let originalServiceAmount = 0;
  let serviceAmount = 0;
  let productAmount = 0;
  
  // Process items exactly like the real service
  posData.items.forEach(item => {
    const itemTotal = item.price * item.quantity;
    
    if (item.type === 'service') {
      originalServiceAmount += itemTotal;
      
      // Apply discount logic
      let itemDiscountAmount = 0;
      if (discountPercentage && discountPercentage > 0) {
        itemDiscountAmount = (itemTotal * discountPercentage) / 100;
      } else if (discountAmount && discountAmount > 0) {
        // Proportional distribution of fixed discount
        const totalServiceAmount = posData.items
          .filter(i => i.type === 'service')
          .reduce((sum, i) => sum + (i.price * i.quantity), 0);
        const itemProportion = itemTotal / totalServiceAmount;
        itemDiscountAmount = discountAmount * itemProportion;
      }
      
      serviceAmount += itemTotal - itemDiscountAmount;
    } else {
      productAmount += itemTotal;
    }
  });
  
  const totalAmount = serviceAmount + productAmount;
  
  console.log('\n📊 Transaction Calculation Results:');
  console.log(`- Original Service Amount: $${originalServiceAmount.toFixed(2)}`);
  console.log(`- Discounted Service Amount: $${serviceAmount.toFixed(2)}`);
  console.log(`- Product Amount: $${productAmount.toFixed(2)}`);
  console.log(`- FINAL TRANSACTION AMOUNT: $${totalAmount.toFixed(2)}`);
  
  return {
    id: `pos-tx-${Date.now()}`,
    amount: totalAmount,
    serviceAmount: serviceAmount,
    productAmount: productAmount,
    originalServiceAmount: originalServiceAmount,
    discountPercentage: discountPercentage,
    discountAmount: discountAmount,
    paymentMethod: paymentMethod,
    items: posData.items,
    metadata: {
      discountApplied: (discountPercentage && discountPercentage > 0) || (discountAmount && discountAmount > 0)
    }
  };
};

console.log('🧪 End-to-End Discount Flow Test...');

// Test scenario: Mixed cart with 15% discount
console.log('\n🛒 Test Scenario: Mixed Cart with 15% Discount');
const cartItems = [
  { id: 'svc-1', name: 'Haircut & Style', type: 'service', quantity: 1, price: 65.00 },
  { id: 'svc-2', name: 'Hair Treatment', type: 'service', quantity: 1, price: 85.00 },
  { id: 'prod-1', name: 'Shampoo', type: 'product', quantity: 1, price: 30.00 },
  { id: 'prod-2', name: 'Conditioner', type: 'product', quantity: 1, price: 25.00 }
];

console.log('\n📝 Cart Contents:');
cartItems.forEach(item => {
  console.log(`- ${item.name} (${item.type}): $${item.price.toFixed(2)} x ${item.quantity}`);
});

// Calculate expected values
const serviceTotal = cartItems.filter(item => item.type === 'service').reduce((sum, item) => sum + (item.price * item.quantity), 0);
const productTotal = cartItems.filter(item => item.type === 'product').reduce((sum, item) => sum + (item.price * item.quantity), 0);
const originalTotal = serviceTotal + productTotal;

console.log(`\n💰 Original Totals:`);
console.log(`- Service Total: $${serviceTotal.toFixed(2)}`);
console.log(`- Product Total: $${productTotal.toFixed(2)}`);
console.log(`- Original Total: $${originalTotal.toFixed(2)}`);

// Apply 15% discount to services only
const discountPercent = 15;
const serviceDiscountAmount = (serviceTotal * discountPercent) / 100;
const discountedServiceTotal = serviceTotal - serviceDiscountAmount;
const finalTotal = discountedServiceTotal + productTotal;

console.log(`\n🎯 Expected After 15% Discount:`);
console.log(`- Service Discount Amount: $${serviceDiscountAmount.toFixed(2)}`);
console.log(`- Discounted Service Total: $${discountedServiceTotal.toFixed(2)}`);
console.log(`- Product Total (unchanged): $${productTotal.toFixed(2)}`);
console.log(`- EXPECTED FINAL TOTAL: $${finalTotal.toFixed(2)}`);

// Simulate the payment dialog calling the POS page
console.log('\n🔄 Simulating Payment Flow...');

// Step 1: Payment dialog calculates discount and calls onComplete
const paymentMethod = 'Credit Card';
const giftCardCode = undefined;
const giftCardAmount = undefined;

console.log('\n📤 Payment Dialog → POS Page:');
console.log(`- Payment Method: ${paymentMethod}`);
console.log(`- Discount Percentage: ${discountPercent}%`);
console.log(`- Service Discount Amount: $${serviceDiscountAmount.toFixed(2)}`);

// Step 2: POS page calls recordPOSTransaction
const posData = {
  id: `pos-${Date.now()}`,
  clientId: 'client-123',
  clientName: 'Test Customer',
  location: 'loc1',
  items: cartItems,
  metadata: { source: 'POS' }
};

// Step 3: recordPOSTransaction calls ConsolidatedTransactionService.createPOSTransaction
const transaction = mockCreatePOSTransaction(
  posData,
  'credit_card',
  discountPercent,
  serviceDiscountAmount
);

// Step 4: Verify the results
console.log('\n✅ Verification:');
console.log(`- Expected Final Total: $${finalTotal.toFixed(2)}`);
console.log(`- Actual Transaction Amount: $${transaction.amount.toFixed(2)}`);
console.log(`- Amounts Match: ${Math.abs(finalTotal - transaction.amount) < 0.01 ? '✅ YES' : '❌ NO'}`);
console.log(`- Discount Applied: ${transaction.metadata.discountApplied ? '✅ YES' : '❌ NO'}`);

if (Math.abs(finalTotal - transaction.amount) < 0.01) {
  console.log('\n🎉 SUCCESS: The transaction amount correctly reflects the discounted total!');
} else {
  console.log('\n❌ FAILURE: The transaction amount does not match the expected discounted total.');
}

console.log('\n📋 What should appear in the accounting dashboard:');
console.log(`- Amount Column: $${transaction.amount.toFixed(2)} (final total after discount)`);
console.log(`- Service Column: $${transaction.serviceAmount.toFixed(2)} (discounted service amount)`);
console.log(`- Product Column: $${transaction.productAmount.toFixed(2)} (full product amount)`);
console.log(`- Transaction Type: consolidated_sale (mixed cart)`);
console.log(`- Source: POS`);
