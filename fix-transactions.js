// Transaction Fix Script
// Run this in the browser console on the accounting page to fix transaction data

function fixTransactionData() {
    console.log('🔧 Starting transaction data fix...');
    
    // Get transactions from localStorage
    const stored = localStorage.getItem('vanity_transactions');
    if (!stored) {
        console.error('❌ No transactions found in localStorage');
        return;
    }
    
    let transactions;
    try {
        transactions = JSON.parse(stored);
    } catch (error) {
        console.error('❌ Failed to parse transactions:', error);
        return;
    }
    
    console.log(`📊 Found ${transactions.length} transactions to process`);
    
    let fixedAmountCount = 0;
    let fixedPaymentMethodCount = 0;
    let reconstructedCount = 0;
    
    transactions.forEach((tx, index) => {
        console.log(`\n🔍 Processing transaction ${index + 1}: ${tx.id}`);
        console.log('Current data:', {
            type: tx.type,
            amount: tx.amount,
            paymentMethod: tx.paymentMethod,
            serviceAmount: tx.serviceAmount,
            productAmount: tx.productAmount,
            discountAmount: tx.discountAmount,
            description: tx.description
        });
        
        // Fix 1: Consolidated transactions with discount but wrong amount
        if (tx.type === 'consolidated_sale' && tx.discountAmount && tx.discountAmount > 0) {
            if (tx.serviceAmount !== undefined && tx.productAmount !== undefined) {
                const expectedAmount = tx.serviceAmount + tx.productAmount;
                if (Math.abs(tx.amount - expectedAmount) > 0.01) {
                    console.log(`💰 Fixing amount: ${tx.amount} -> ${expectedAmount}`);
                    tx.amount = expectedAmount;
                    fixedAmountCount++;
                }
            }
        }
        
        // Fix 2: Payment method corrections
        if (tx.paymentMethod === 'cash') {
            if (tx.description && tx.description.toLowerCase().includes('mobile')) {
                console.log(`📱 Fixing payment method: cash -> mobile_payment`);
                tx.paymentMethod = 'mobile_payment';
                fixedPaymentMethodCount++;
            } else if (tx.description && tx.description.toLowerCase().includes('card')) {
                console.log(`💳 Fixing payment method: cash -> credit_card`);
                tx.paymentMethod = 'credit_card';
                fixedPaymentMethodCount++;
            }
        }
        
        // Fix 3: Reconstruct missing discount fields for consolidated transactions
        if (tx.type === 'consolidated_sale' && !tx.serviceAmount && !tx.productAmount) {
            if (tx.metadata && tx.metadata.originalTotal && tx.metadata.finalTotal) {
                const originalTotal = tx.metadata.originalTotal;
                const finalTotal = tx.metadata.finalTotal;
                const discountAmount = originalTotal - finalTotal;
                
                if (discountAmount > 0) {
                    console.log(`🔨 Reconstructing discount fields for transaction with discount`);
                    tx.originalServiceAmount = originalTotal;
                    tx.serviceAmount = finalTotal;
                    tx.productAmount = 0;
                    tx.discountAmount = discountAmount;
                    tx.discountPercentage = Math.round((discountAmount / originalTotal) * 100 * 100) / 100; // Round to 2 decimals
                    tx.amount = finalTotal;
                    reconstructedCount++;
                    fixedAmountCount++;
                }
            }
        }
        
        // Fix 4: Handle transactions that should have discounted amounts but don't
        if (tx.type === 'consolidated_sale' && tx.description && tx.description.includes('discount')) {
            // Try to extract discount info from description
            const discountMatch = tx.description.match(/(\d+(?:\.\d+)?)%\s*discount/i);
            if (discountMatch && !tx.discountAmount) {
                const discountPercentage = parseFloat(discountMatch[1]);
                const discountAmount = (tx.amount * discountPercentage) / 100;
                const originalAmount = tx.amount + discountAmount;
                
                console.log(`🔍 Found discount in description, reconstructing: ${discountPercentage}%`);
                tx.originalServiceAmount = originalAmount;
                tx.serviceAmount = tx.amount;
                tx.productAmount = 0;
                tx.discountAmount = discountAmount;
                tx.discountPercentage = discountPercentage;
                // Amount stays the same as it's already the discounted amount
                reconstructedCount++;
            }
        }
        
        console.log('Updated data:', {
            type: tx.type,
            amount: tx.amount,
            paymentMethod: tx.paymentMethod,
            serviceAmount: tx.serviceAmount,
            productAmount: tx.productAmount,
            discountAmount: tx.discountAmount
        });
    });
    
    // Save the fixed transactions back to localStorage
    try {
        localStorage.setItem('vanity_transactions', JSON.stringify(transactions));
        console.log(`\n✅ Transaction fix completed!`);
        console.log(`📊 Summary:`);
        console.log(`   - Fixed ${fixedAmountCount} transaction amounts`);
        console.log(`   - Fixed ${fixedPaymentMethodCount} payment methods`);
        console.log(`   - Reconstructed ${reconstructedCount} transactions with missing discount data`);
        console.log(`\n🔄 Please refresh the accounting page to see the changes.`);
        
        // Trigger a page refresh after a short delay
        setTimeout(() => {
            window.location.reload();
        }, 2000);
        
    } catch (error) {
        console.error('❌ Failed to save fixed transactions:', error);
    }
}

// Run the fix
console.log('🚀 Running transaction data fix...');
fixTransactionData();
