<svg width="1200" height="600" viewBox="0 0 1200 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#e2e8f0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cbd5e1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#be185d;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="600" fill="url(#bgGradient)"/>
  
  <!-- Decorative elements -->
  <circle cx="100" cy="100" r="40" fill="#f1f5f9" opacity="0.6"/>
  <circle cx="1100" cy="500" r="60" fill="#f1f5f9" opacity="0.4"/>
  <circle cx="200" cy="450" r="25" fill="#e2e8f0" opacity="0.8"/>
  <circle cx="1000" cy="150" r="35" fill="#e2e8f0" opacity="0.6"/>
  
  <!-- Main icon -->
  <g transform="translate(550, 250)">
    <!-- Camera/Image icon -->
    <rect x="0" y="20" width="100" height="80" rx="8" fill="url(#iconGradient)" opacity="0.8"/>
    <circle cx="50" cy="60" r="20" fill="white" opacity="0.9"/>
    <circle cx="50" cy="60" r="12" fill="url(#iconGradient)"/>
    <rect x="15" y="0" width="20" height="15" rx="3" fill="url(#iconGradient)" opacity="0.6"/>
  </g>
  
  <!-- Text -->
  <text x="600" y="380" text-anchor="middle" fill="#64748b" font-family="system-ui, -apple-system, sans-serif" font-size="24" font-weight="500">
    Featured Content
  </text>
  <text x="600" y="410" text-anchor="middle" fill="#94a3b8" font-family="system-ui, -apple-system, sans-serif" font-size="16">
    Beautiful content will appear here
  </text>
  
  <!-- Subtle pattern -->
  <pattern id="dots" patternUnits="userSpaceOnUse" width="40" height="40">
    <circle cx="20" cy="20" r="1" fill="#e2e8f0" opacity="0.3"/>
  </pattern>
  <rect width="1200" height="600" fill="url(#dots)"/>
</svg>
