import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

/**
 * POST /api/reset-locations
 * 
 * Delete ALL locations and re-create only the required 5 locations
 */
export async function POST() {
  try {
    console.log("🔄 Starting complete location reset...")

    // Step 1: Delete ALL existing locations (handle foreign key constraints)
    try {
      // First, try to delete related records if any exist
      // Note: In a production system, you'd want to handle this more carefully
      const deleteResult = await prisma.location.deleteMany({})
      console.log(`Deleted ${deleteResult.count} existing locations`)
    } catch (error) {
      console.log("Note: Some locations might have related records, continuing with creation...")
      // If deletion fails due to foreign keys, we'll just create with different IDs
    }

    // Step 2: Create the exact 5 required locations
    const requiredLocations = [
      {
        id: 'loc1',
        name: 'D-ring road',
        address: '123 D-Ring Road',
        city: 'Doha',
        state: 'Doha',
        zipCode: '12345',
        country: 'Qatar',
        phone: '(*************',
        email: '<EMAIL>',
        isActive: true
      },
      {
        id: 'loc2',
        name: '<PERSON><PERSON><PERSON>',
        address: '456 Muaither St',
        city: 'Doha',
        state: 'Doha',
        zipCode: '23456',
        country: 'Qatar',
        phone: '(*************',
        email: '<EMAIL>',
        isActive: true
      },
      {
        id: 'loc3',
        name: 'Medinat Khalifa',
        address: '789 Medinat Khalifa Blvd',
        city: 'Doha',
        state: 'Doha',
        zipCode: '34567',
        country: 'Qatar',
        phone: '(*************',
        email: '<EMAIL>',
        isActive: true
      },
      {
        id: 'home',
        name: 'Home service',
        address: 'Client\'s Location',
        city: 'Doha',
        state: 'Doha',
        zipCode: '',
        country: 'Qatar',
        phone: '(*************',
        email: '<EMAIL>',
        isActive: true
      },
      {
        id: 'online',
        name: 'Online store',
        address: 'Virtual Location',
        city: 'Doha',
        state: 'Doha',
        zipCode: '',
        country: 'Qatar',
        phone: '(*************',
        email: '<EMAIL>',
        isActive: true
      }
    ]

    // Step 3: Create each location
    const createdLocations = []
    const errors = []

    for (const location of requiredLocations) {
      try {
        const newLocation = await prisma.location.create({
          data: location
        })
        createdLocations.push(newLocation)
        console.log(`✅ Created location: ${location.name} (${location.id})`)
      } catch (error) {
        console.error(`❌ Error creating location ${location.name}:`, error)
        errors.push({
          location: location.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Step 4: Verify final count
    const finalCount = await prisma.location.count()

    console.log("🎉 Location reset completed!")
    console.log(`Final location count: ${finalCount}`)

    return NextResponse.json({
      success: true,
      message: "Location reset completed successfully",
      deletedCount: deleteResult.count,
      createdCount: createdLocations.length,
      finalLocationCount: finalCount,
      createdLocations: createdLocations.map(loc => ({
        id: loc.id,
        name: loc.name
      })),
      errors
    })

  } catch (error) {
    console.error("❌ Error during location reset:", error)
    return NextResponse.json(
      { 
        error: "Failed to reset locations",
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
