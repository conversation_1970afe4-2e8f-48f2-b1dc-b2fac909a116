"use client"

import { useAuth } from "@/lib/auth-provider"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CalendarIcon, DollarSign, Users, Clock } from "lucide-react"

export function StatsCards() {
  const { currentLocation } = useAuth()

  // DEPRECATED: Stats should be fetched from real API endpoints
  // Use the enhanced StatsCards component in components/dashboard/stats-cards.tsx instead
  const stats = {
    appointments: 0,
    revenue: 0,
    newClients: 0,
    utilization: 0,
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Today's Appointments</CardTitle>
          <CalendarIcon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.appointments}</div>
          <p className="text-xs text-muted-foreground">+2 from yesterday</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${stats.revenue}</div>
          <p className="text-xs text-muted-foreground">+$340 from yesterday</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">New Clients (Week)</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.newClients}</div>
          <p className="text-xs text-muted-foreground">+2 from last week</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Staff Utilization</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.utilization}%</div>
          <p className="text-xs text-muted-foreground">+4% from last week</p>
        </CardContent>
      </Card>
    </div>
  )
}

