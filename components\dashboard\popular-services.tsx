"use client"

import { useState, useEffect, useMemo } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { useTransactions } from "@/lib/transaction-provider"
import { useAuth } from "@/lib/auth-provider"
import { useRealTimeEvent } from "@/hooks/use-real-time-updates"
import { RealTimeEventType } from "@/lib/real-time-service"
import { TransactionStatus, TransactionType } from "@/lib/transaction-types"
import { getAllAppointments } from "@/lib/appointment-service"
import { startOfMonth } from "date-fns"

interface ServicePopularity {
  name: string
  count: number
  percentage: number
}

export function PopularServices() {
  const { transactions, filterTransactions } = useTransactions()
  const { currentLocation } = useAuth()
  const [refreshKey, setRefreshKey] = useState(0)

  // Calculate popular services from transactions and appointments
  const popularServices = useMemo(() => {
    const serviceMap = new Map<string, number>()

    // Get transactions from this month
    const filters: any = {
      startDate: startOfMonth(new Date()),
      endDate: new Date()
    }
    if (currentLocation !== 'all') {
      filters.location = currentLocation
    }

    const monthlyTxs = filterTransactions(filters)

    // Count services from transactions
    monthlyTxs
      .filter(t =>
        t.status === TransactionStatus.COMPLETED &&
        (t.type === TransactionType.SERVICE_SALE || t.type === TransactionType.CONSOLIDATED_SALE)
      )
      .forEach(transaction => {
        if (transaction.type === TransactionType.CONSOLIDATED_SALE && transaction.items) {
          // For consolidated sales, count each service item
          transaction.items
            .filter(item => item.type === 'service')
            .forEach(item => {
              const count = serviceMap.get(item.name) || 0
              serviceMap.set(item.name, count + item.quantity)
            })
        } else {
          // For regular service sales
          const serviceName = transaction.description || 'Service'
          const count = serviceMap.get(serviceName) || 0
          serviceMap.set(serviceName, count + 1)
        }
      })

    // Also count from appointments (for additional context)
    try {
      const appointments = getAllAppointments()
      appointments
        .filter(apt => {
          const aptDate = new Date(apt.date)
          const monthStart = startOfMonth(new Date())
          return aptDate >= monthStart &&
                 apt.status === 'completed' &&
                 (currentLocation === 'all' || apt.location === currentLocation)
        })
        .forEach(appointment => {
          if (appointment.services && appointment.services.length > 0) {
            appointment.services.forEach(service => {
              const count = serviceMap.get(service.name) || 0
              serviceMap.set(service.name, count + 1)
            })
          } else if (appointment.service) {
            const count = serviceMap.get(appointment.service) || 0
            serviceMap.set(appointment.service, count + 1)
          }
        })
    } catch (error) {
      console.log('Could not load appointments for service popularity:', error)
    }

    // Convert to array and calculate percentages
    const services = Array.from(serviceMap.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 4) // Top 4 services

    const totalCount = services.reduce((sum, service) => sum + service.count, 0)

    return services.map(service => ({
      ...service,
      percentage: totalCount > 0 ? Math.round((service.count / totalCount) * 100) : 0
    }))
  }, [transactions, currentLocation, filterTransactions, refreshKey])

  // Real-time event listeners for automatic updates
  useRealTimeEvent(RealTimeEventType.TRANSACTION_CREATED, () => {
    setRefreshKey(prev => prev + 1)
  }, [])

  useRealTimeEvent(RealTimeEventType.TRANSACTION_UPDATED, () => {
    setRefreshKey(prev => prev + 1)
  }, [])

  useRealTimeEvent(RealTimeEventType.APPOINTMENT_CREATED, () => {
    setRefreshKey(prev => prev + 1)
  }, [])

  useRealTimeEvent(RealTimeEventType.APPOINTMENT_UPDATED, () => {
    setRefreshKey(prev => prev + 1)
  }, [])

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-xl font-bold">Popular Services</CardTitle>
        <p className="text-sm text-muted-foreground">Top services by appointment count.</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {popularServices.length > 0 ? (
            popularServices.map((service, index) => (
              <div key={`${service.name}-${index}`}>
                <div className="flex justify-between mb-1">
                  <span className="truncate" title={service.name}>{service.name}</span>
                  <span>{service.percentage}%</span>
                </div>
                <Progress value={service.percentage} className="h-2" />
              </div>
            ))
          ) : (
            <div className="text-center text-muted-foreground py-4">
              No service data available
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

