"use client"

import { useState, useEffect, useMemo } from "react"
import { useTheme } from "next-themes"
import { useAuth } from "@/lib/auth-provider"
import { useCurrency } from "@/lib/currency-provider"
import { useTransactions } from "@/lib/transaction-provider"
import { useRealTimeEvent } from "@/hooks/use-real-time-updates"
import { RealTimeEventType } from "@/lib/real-time-service"
import { TransactionType, TransactionStatus } from "@/lib/transaction-types"
import { format, eachDayOfInterval, eachWeekOfInterval, eachMonthOfInterval, isSameDay, isSameMonth, startOfDay } from "date-fns"
import { BarChart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from "recharts"

interface RevenueChartProps {
  dateRange?: {
    from: Date;
    to: Date;
  };
}

export function RevenueChart({ dateRange }: RevenueChartProps) {
  const { theme } = useTheme()
  const { currentLocation } = useAuth()
  const { currency, formatCurrency } = useCurrency()
  const { transactions, filterTransactions } = useTransactions()
  const [refreshKey, setRefreshKey] = useState(0)

  // Real-time event listeners for automatic updates
  useRealTimeEvent(RealTimeEventType.TRANSACTION_CREATED, () => {
    setRefreshKey(prev => prev + 1)
  }, [])

  useRealTimeEvent(RealTimeEventType.TRANSACTION_UPDATED, () => {
    setRefreshKey(prev => prev + 1)
  }, [])

  // Generate data based on real transaction data
  const generateChartData = useMemo(() => {
    if (!dateRange?.from || !dateRange?.to) {
      return defaultData
    }

    const { from, to } = dateRange
    const daysDiff = Math.ceil((to.getTime() - from.getTime()) / (1000 * 60 * 60 * 24))

    // Filter transactions for the date range and location
    const filters: any = {
      startDate: from,
      endDate: to
    }
    if (currentLocation !== 'all') {
      filters.location = currentLocation
    }

    const filteredTxs = filterTransactions(filters).filter(t =>
      t.status === TransactionStatus.COMPLETED
    )

    // Helper function to calculate revenue for a specific date
    const getRevenueForDate = (targetDate: Date) => {
      const dayTransactions = filteredTxs.filter(t => {
        const txDate = new Date(t.date)
        return isSameDay(txDate, targetDate)
      })

      let serviceRevenue = 0
      let productRevenue = 0

      dayTransactions.forEach(tx => {
        if (tx.type === TransactionType.SERVICE_SALE) {
          serviceRevenue += tx.amount
        } else if (tx.type === TransactionType.PRODUCT_SALE) {
          productRevenue += tx.amount
        } else if (tx.type === TransactionType.CONSOLIDATED_SALE) {
          // For consolidated sales, use the breakdown if available
          if (tx.serviceAmount) serviceRevenue += tx.serviceAmount
          if (tx.productAmount) productRevenue += tx.productAmount
          // If no breakdown, assume it's mostly service
          if (!tx.serviceAmount && !tx.productAmount) {
            serviceRevenue += tx.amount * 0.7
            productRevenue += tx.amount * 0.3
          }
        } else {
          // Other transaction types - assume service
          serviceRevenue += tx.amount
        }
      })

      return { serviceRevenue, productRevenue }
    }

    // For different time periods, use different grouping
    if (daysDiff <= 31) {
      // Up to a month - show daily data
      return eachDayOfInterval({ start: from, end: to }).map(date => {
        const { serviceRevenue, productRevenue } = getRevenueForDate(date)
        return {
          name: format(date, "MMM d"),
          date,
          services: serviceRevenue,
          products: productRevenue,
          total: serviceRevenue + productRevenue
        }
      })
    } else if (daysDiff <= 90) {
      // Up to 3 months - show weekly data
      return eachWeekOfInterval({ start: from, end: to }, { weekStartsOn: 1 }).map(date => {
        // Calculate weekly totals
        const weekEnd = new Date(date)
        weekEnd.setDate(weekEnd.getDate() + 6)

        const weekTransactions = filteredTxs.filter(t => {
          const txDate = new Date(t.date)
          return txDate >= date && txDate <= weekEnd
        })

        let serviceRevenue = 0
        let productRevenue = 0

        weekTransactions.forEach(tx => {
          if (tx.type === TransactionType.SERVICE_SALE) {
            serviceRevenue += tx.amount
          } else if (tx.type === TransactionType.PRODUCT_SALE) {
            productRevenue += tx.amount
          } else if (tx.type === TransactionType.CONSOLIDATED_SALE) {
            if (tx.serviceAmount) serviceRevenue += tx.serviceAmount
            if (tx.productAmount) productRevenue += tx.productAmount
            if (!tx.serviceAmount && !tx.productAmount) {
              serviceRevenue += tx.amount * 0.7
              productRevenue += tx.amount * 0.3
            }
          } else {
            serviceRevenue += tx.amount
          }
        })

        return {
          name: `Week of ${format(date, "MMM d")}`,
          date,
          services: serviceRevenue,
          products: productRevenue,
          total: serviceRevenue + productRevenue
        }
      })
    } else {
      // More than 3 months - show monthly data
      return eachMonthOfInterval({ start: from, end: to }).map(date => {
        const monthTransactions = filteredTxs.filter(t => {
          const txDate = new Date(t.date)
          return isSameMonth(txDate, date)
        })

        let serviceRevenue = 0
        let productRevenue = 0

        monthTransactions.forEach(tx => {
          if (tx.type === TransactionType.SERVICE_SALE) {
            serviceRevenue += tx.amount
          } else if (tx.type === TransactionType.PRODUCT_SALE) {
            productRevenue += tx.amount
          } else if (tx.type === TransactionType.CONSOLIDATED_SALE) {
            if (tx.serviceAmount) serviceRevenue += tx.serviceAmount
            if (tx.productAmount) productRevenue += tx.productAmount
            if (!tx.serviceAmount && !tx.productAmount) {
              serviceRevenue += tx.amount * 0.7
              productRevenue += tx.amount * 0.3
            }
          } else {
            serviceRevenue += tx.amount
          }
        })

        return {
          name: format(date, "MMM yyyy"),
          date,
          services: serviceRevenue,
          products: productRevenue,
          total: serviceRevenue + productRevenue
        }
      })
    }
  }, [dateRange, currentLocation, transactions, filterTransactions, refreshKey])

  // Default monthly data
  const defaultData = [
    { name: "Jan", services: 1750, products: 750, total: 2500 },
    { name: "Feb", services: 2100, products: 900, total: 3000 },
    { name: "Mar", services: 1960, products: 840, total: 2800 },
    { name: "Apr", services: 2240, products: 960, total: 3200 },
    { name: "May", services: 2800, products: 1200, total: 4000 },
    { name: "Jun", services: 2660, products: 1140, total: 3800 },
    { name: "Jul", services: 2940, products: 1260, total: 4200 },
    { name: "Aug", services: 3150, products: 1350, total: 4500 },
    { name: "Sep", services: 3360, products: 1440, total: 4800 },
    { name: "Oct", services: 3500, products: 1500, total: 5000 },
    { name: "Nov", services: 3290, products: 1410, total: 4700 },
    { name: "Dec", services: 3640, products: 1560, total: 5200 },
  ]

  const data = generateChartData

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={data}>
          <XAxis dataKey="name" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
          <YAxis
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `${currency.symbol}${value}`}
          />
          <Tooltip
            formatter={(value, name) => {
              const formattedName = name === 'services' ? 'Services' : name === 'products' ? 'Products' : 'Total'
              return [`${currency.symbol}${value}`, formattedName]
            }}
            contentStyle={{
              backgroundColor: theme === "dark" ? "#1f2937" : "#ffffff",
              borderColor: theme === "dark" ? "#374151" : "#e5e7eb",
            }}
          />
          <Legend />
          <Bar dataKey="services" name="Services" fill="#8884d8" radius={[4, 4, 0, 0]} />
          <Bar dataKey="products" name="Products" fill="#82ca9d" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

