"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertTriangle, Calendar, Download, Edit, Eye, FileText, Plus, Search, Trash2, Upload, AlertCircle, CheckCircle } from 'lucide-react'
import { format, parseISO, differenceInDays } from 'date-fns'
import { useAuth } from "@/lib/auth-provider"
import { CompanyDocumentStorage, CompanyDocument, CompanyDocumentType, CompanyDocumentStatus } from "@/lib/company-document-storage"

interface CompanyDocumentsProps {
  className?: string
}

export function CompanyDocuments({ className }: CompanyDocumentsProps) {
  const { user, hasPermission } = useAuth()
  const [documents, setDocuments] = useState<CompanyDocument[]>([])
  const [documentTypes, setDocumentTypes] = useState<CompanyDocumentType[]>([])
  const [filteredDocuments, setFilteredDocuments] = useState<CompanyDocument[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedDocument, setSelectedDocument] = useState<CompanyDocument | null>(null)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    documentType: '',
    documentName: '',
    documentNumber: '',
    issueDate: '',
    expiryDate: '',
    notes: '',
    documentFile: null as File | null
  })

  // Load data on component mount
  useEffect(() => {
    loadData()
    // Update document statuses on load
    CompanyDocumentStorage.updateDocumentStatuses()
  }, [])

  // Filter documents when search term or filters change
  useEffect(() => {
    filterDocuments()
  }, [documents, searchTerm, statusFilter, categoryFilter])

  const loadData = () => {
    const docs = CompanyDocumentStorage.getCompanyDocuments()
    const types = CompanyDocumentStorage.getDocumentTypes()
    setDocuments(docs)
    setDocumentTypes(types)
  }

  const filterDocuments = () => {
    let filtered = documents

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(doc =>
        doc.documentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.documentTypeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.documentNumber.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(doc => doc.status === statusFilter)
    }

    // Category filter
    if (categoryFilter !== 'all') {
      const categoryTypes = documentTypes.filter(type => type.category === categoryFilter)
      filtered = filtered.filter(doc => categoryTypes.some(type => type.id === doc.documentType))
    }

    setFilteredDocuments(filtered)
  }

  const handleAddDocument = async () => {
    if (!formData.documentType || !formData.documentName || !formData.expiryDate) {
      alert('Please fill in all required fields')
      return
    }

    const documentType = documentTypes.find(type => type.id === formData.documentType)
    if (!documentType) {
      alert('Invalid document type')
      return
    }

    try {
      const newDocument = CompanyDocumentStorage.addCompanyDocument({
        documentType: formData.documentType,
        documentTypeName: documentType.name,
        documentName: formData.documentName,
        documentNumber: formData.documentNumber,
        issueDate: formData.issueDate,
        expiryDate: formData.expiryDate,
        notes: formData.notes,
        uploadedBy: user?.name || 'Unknown User',
        fileName: formData.documentFile?.name,
        fileSize: formData.documentFile ? `${(formData.documentFile.size / 1024).toFixed(1)} KB` : undefined,
        fileType: formData.documentFile?.type
      })

      loadData()
      setIsAddDialogOpen(false)
      resetForm()
    } catch (error) {
      console.error('Error adding document:', error)
      alert('Error adding document')
    }
  }

  const handleEditDocument = async () => {
    if (!selectedDocument || !formData.documentType || !formData.documentName || !formData.expiryDate) {
      alert('Please fill in all required fields')
      return
    }

    const documentType = documentTypes.find(type => type.id === formData.documentType)
    if (!documentType) {
      alert('Invalid document type')
      return
    }

    try {
      const updatedDocument: CompanyDocument = {
        ...selectedDocument,
        documentType: formData.documentType,
        documentTypeName: documentType.name,
        documentName: formData.documentName,
        documentNumber: formData.documentNumber,
        issueDate: formData.issueDate,
        expiryDate: formData.expiryDate,
        notes: formData.notes,
        uploadedBy: user?.name || 'Unknown User',
        fileName: formData.documentFile?.name || selectedDocument.fileName,
        fileSize: formData.documentFile ? `${(formData.documentFile.size / 1024).toFixed(1)} KB` : selectedDocument.fileSize,
        fileType: formData.documentFile?.type || selectedDocument.fileType
      }

      CompanyDocumentStorage.updateCompanyDocument(updatedDocument)
      loadData()
      setIsEditDialogOpen(false)
      setSelectedDocument(null)
      resetForm()
    } catch (error) {
      console.error('Error updating document:', error)
      alert('Error updating document')
    }
  }

  const handleDeleteDocument = (document: CompanyDocument) => {
    if (confirm(`Are you sure you want to delete "${document.documentName}"?`)) {
      CompanyDocumentStorage.deleteCompanyDocument(document.id)
      loadData()
    }
  }

  const handleViewDocument = (document: CompanyDocument) => {
    setSelectedDocument(document)
    setIsViewDialogOpen(true)
  }

  const handleEditClick = (document: CompanyDocument) => {
    setSelectedDocument(document)
    setFormData({
      documentType: document.documentType,
      documentName: document.documentName,
      documentNumber: document.documentNumber,
      issueDate: document.issueDate,
      expiryDate: document.expiryDate,
      notes: document.notes || '',
      documentFile: null
    })
    setIsEditDialogOpen(true)
  }

  const resetForm = () => {
    setFormData({
      documentType: '',
      documentName: '',
      documentNumber: '',
      issueDate: '',
      expiryDate: '',
      notes: '',
      documentFile: null
    })
  }

  const getStatusBadge = (status: CompanyDocumentStatus) => {
    switch (status) {
      case CompanyDocumentStatus.VALID:
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Valid</Badge>
      case CompanyDocumentStatus.EXPIRING_SOON:
        return <Badge variant="default" className="bg-yellow-100 text-yellow-800"><AlertTriangle className="w-3 h-3 mr-1" />Expiring Soon</Badge>
      case CompanyDocumentStatus.EXPIRED:
        return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Expired</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  const getDaysUntilExpiry = (expiryDate: string) => {
    const days = differenceInDays(parseISO(expiryDate), new Date())
    if (days < 0) {
      return `${Math.abs(days)} days overdue`
    } else if (days === 0) {
      return 'Expires today'
    } else {
      return `${days} days remaining`
    }
  }

  const getExpirySummary = () => {
    const summary = CompanyDocumentStorage.getExpirySummary()
    return summary
  }

  // Check if user has permission to manage company documents
  if (!hasPermission("manage_company_documents") && !hasPermission("all")) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Access Denied</CardTitle>
          <CardDescription>You don't have permission to manage company documents.</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  const expirySummary = getExpirySummary()

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{expirySummary.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Valid Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {expirySummary.total - expirySummary.expired - expirySummary.expiringSoon}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{expirySummary.expiringSoon}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Expired</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{expirySummary.expired}</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Official Company Documents</CardTitle>
              <CardDescription>
                Manage licenses, certifications, insurance policies, and other official company documents
              </CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => setIsAddDialogOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Document
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add Company Document</DialogTitle>
                  <DialogDescription>
                    Upload a new official company document
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="documentType">Document Type *</Label>
                      <Select value={formData.documentType} onValueChange={(value) => setFormData(prev => ({ ...prev, documentType: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select document type" />
                        </SelectTrigger>
                        <SelectContent>
                          {documentTypes.map(type => (
                            <SelectItem key={type.id} value={type.id}>
                              {type.name} ({type.category})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="documentName">Document Name *</Label>
                      <Input
                        id="documentName"
                        value={formData.documentName}
                        onChange={(e) => setFormData(prev => ({ ...prev, documentName: e.target.value }))}
                        placeholder="Enter document name"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="documentNumber">Document Number</Label>
                      <Input
                        id="documentNumber"
                        value={formData.documentNumber}
                        onChange={(e) => setFormData(prev => ({ ...prev, documentNumber: e.target.value }))}
                        placeholder="Enter document number"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="issueDate">Issue Date</Label>
                      <Input
                        id="issueDate"
                        type="date"
                        value={formData.issueDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, issueDate: e.target.value }))}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="expiryDate">Expiry Date *</Label>
                      <Input
                        id="expiryDate"
                        type="date"
                        value={formData.expiryDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, expiryDate: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="documentFile">Document File</Label>
                      <Input
                        id="documentFile"
                        type="file"
                        onChange={(e) => setFormData(prev => ({ ...prev, documentFile: e.target.files?.[0] || null }))}
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      placeholder="Additional notes about this document"
                      rows={3}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddDocument}>
                    Add Document
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value={CompanyDocumentStatus.VALID}>Valid</SelectItem>
                <SelectItem value={CompanyDocumentStatus.EXPIRING_SOON}>Expiring Soon</SelectItem>
                <SelectItem value={CompanyDocumentStatus.EXPIRED}>Expired</SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="license">Licenses</SelectItem>
                <SelectItem value="certification">Certifications</SelectItem>
                <SelectItem value="insurance">Insurance</SelectItem>
                <SelectItem value="contract">Contracts</SelectItem>
                <SelectItem value="permit">Permits</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Documents Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Document Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Document Number</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Expiry Date</TableHead>
                  <TableHead>Days Until Expiry</TableHead>
                  <TableHead>Version</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDocuments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                      No documents found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredDocuments.map((document) => (
                    <TableRow key={document.id}>
                      <TableCell className="font-medium">{document.documentName}</TableCell>
                      <TableCell>{document.documentTypeName}</TableCell>
                      <TableCell>{document.documentNumber || '-'}</TableCell>
                      <TableCell>{getStatusBadge(document.status)}</TableCell>
                      <TableCell>{format(parseISO(document.expiryDate), 'MMM dd, yyyy')}</TableCell>
                      <TableCell className={
                        document.status === CompanyDocumentStatus.EXPIRED ? 'text-red-600' :
                        document.status === CompanyDocumentStatus.EXPIRING_SOON ? 'text-yellow-600' :
                        'text-green-600'
                      }>
                        {getDaysUntilExpiry(document.expiryDate)}
                      </TableCell>
                      <TableCell>v{document.version}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDocument(document)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditClick(document)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteDocument(document)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Company Document</DialogTitle>
            <DialogDescription>
              Update the company document information
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-documentType">Document Type *</Label>
                <Select value={formData.documentType} onValueChange={(value) => setFormData(prev => ({ ...prev, documentType: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select document type" />
                  </SelectTrigger>
                  <SelectContent>
                    {documentTypes.map(type => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name} ({type.category})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-documentName">Document Name *</Label>
                <Input
                  id="edit-documentName"
                  value={formData.documentName}
                  onChange={(e) => setFormData(prev => ({ ...prev, documentName: e.target.value }))}
                  placeholder="Enter document name"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-documentNumber">Document Number</Label>
                <Input
                  id="edit-documentNumber"
                  value={formData.documentNumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, documentNumber: e.target.value }))}
                  placeholder="Enter document number"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-issueDate">Issue Date</Label>
                <Input
                  id="edit-issueDate"
                  type="date"
                  value={formData.issueDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, issueDate: e.target.value }))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-expiryDate">Expiry Date *</Label>
                <Input
                  id="edit-expiryDate"
                  type="date"
                  value={formData.expiryDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, expiryDate: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-documentFile">Update Document File</Label>
                <Input
                  id="edit-documentFile"
                  type="file"
                  onChange={(e) => setFormData(prev => ({ ...prev, documentFile: e.target.files?.[0] || null }))}
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-notes">Notes</Label>
              <Textarea
                id="edit-notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Additional notes about this document"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditDocument}>
              Update Document
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Document Details</DialogTitle>
            <DialogDescription>
              View company document information
            </DialogDescription>
          </DialogHeader>
          {selectedDocument && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Document Name</Label>
                  <p className="text-sm text-muted-foreground">{selectedDocument.documentName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Document Type</Label>
                  <p className="text-sm text-muted-foreground">{selectedDocument.documentTypeName}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Document Number</Label>
                  <p className="text-sm text-muted-foreground">{selectedDocument.documentNumber || '-'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <div className="mt-1">{getStatusBadge(selectedDocument.status)}</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Issue Date</Label>
                  <p className="text-sm text-muted-foreground">
                    {selectedDocument.issueDate ? format(parseISO(selectedDocument.issueDate), 'MMM dd, yyyy') : '-'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Expiry Date</Label>
                  <p className="text-sm text-muted-foreground">
                    {format(parseISO(selectedDocument.expiryDate), 'MMM dd, yyyy')}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Uploaded By</Label>
                  <p className="text-sm text-muted-foreground">{selectedDocument.uploadedBy}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Version</Label>
                  <p className="text-sm text-muted-foreground">v{selectedDocument.version}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Upload Date</Label>
                  <p className="text-sm text-muted-foreground">
                    {format(parseISO(selectedDocument.uploadedAt), 'MMM dd, yyyy HH:mm')}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Last Updated</Label>
                  <p className="text-sm text-muted-foreground">
                    {format(parseISO(selectedDocument.updatedAt), 'MMM dd, yyyy HH:mm')}
                  </p>
                </div>
              </div>
              {selectedDocument.fileName && (
                <div>
                  <Label className="text-sm font-medium">File Information</Label>
                  <p className="text-sm text-muted-foreground">
                    {selectedDocument.fileName} ({selectedDocument.fileSize})
                  </p>
                </div>
              )}
              {selectedDocument.notes && (
                <div>
                  <Label className="text-sm font-medium">Notes</Label>
                  <p className="text-sm text-muted-foreground">{selectedDocument.notes}</p>
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
