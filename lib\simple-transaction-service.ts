import { Transaction, TransactionType, TransactionStatus, TransactionSource, PaymentMethod } from './transaction-types';

/**
 * Simple Transaction Service
 * Creates transactions with final amounts only - no complex discount metadata
 */
export class SimpleTransactionService {
  
  /**
   * Generate a unique transaction ID
   */
  static generateTransactionId(): string {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    return `TXN-${timestamp}-${randomSuffix}`;
  }

  /**
   * Create a consolidated transaction from appointment data
   * Records the exact final amount paid (post-discount) with service/product breakdown
   */
  static createAppointmentTransaction(
    appointment: any,
    paymentMethod: PaymentMethod,
    finalAmount: number
  ): Transaction {
    console.log('🏗️ Creating appointment transaction:', {
      appointmentId: appointment.id,
      paymentMethod,
      finalAmount,
      service: appointment.service
    });

    const transactionId = this.generateTransactionId();

    // Calculate service and product amounts
    const serviceAmount = this.calculateServiceTotal(appointment);
    const productAmount = this.calculateProductTotal(appointment);
    const originalTotal = serviceAmount + productAmount;

    // Calculate discount information
    const discountAmount = originalTotal - finalAmount;
    const hasDiscount = discountAmount > 0;

    // Determine transaction type based on items
    let transactionType = TransactionType.SERVICE_SALE;
    let category = "Service Sale";

    if (serviceAmount > 0 && productAmount > 0) {
      transactionType = TransactionType.CONSOLIDATED_SALE;
      category = "Consolidated Sale";
    } else if (productAmount > 0 && serviceAmount === 0) {
      transactionType = TransactionType.PRODUCT_SALE;
      category = "Product Sale";
    }

    // Create description
    let description = `${appointment.service} for ${appointment.clientName}`;
    if (hasDiscount) {
      const discountPercent = ((discountAmount / serviceAmount) * 100).toFixed(1);
      description += ` (${discountPercent}% service discount applied)`;
    }

    const transaction: Transaction = {
      id: transactionId,
      date: new Date(),
      clientId: appointment.clientId,
      clientName: appointment.clientName,
      staffId: appointment.staffId,
      staffName: appointment.staffName,
      type: transactionType,
      category: category,
      description: description,
      amount: finalAmount,
      paymentMethod,
      status: TransactionStatus.COMPLETED,
      location: appointment.location || "loc1",
      source: TransactionSource.CALENDAR,
      reference: {
        type: "appointment",
        id: appointment.id
      },
      // Add consolidated transaction fields - store final amounts
      serviceAmount: hasDiscount ? serviceAmount - discountAmount : serviceAmount,
      productAmount: productAmount,
      originalServiceAmount: serviceAmount,
      discountAmount: hasDiscount ? discountAmount : undefined,
      discountPercentage: hasDiscount ? parseFloat(((discountAmount / serviceAmount) * 100).toFixed(1)) : undefined,
      // Add metadata for legacy compatibility
      metadata: hasDiscount ? {
        discountApplied: true,
        originalTotal: originalTotal,
        discountAmount: discountAmount,
        serviceDiscountOnly: true
      } : undefined,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('✅ Created consolidated transaction:', {
      id: transaction.id,
      type: transactionType,
      finalAmount: finalAmount,
      serviceAmount: transaction.serviceAmount,
      productAmount: productAmount,
      discountAmount: discountAmount,
      hasDiscount: hasDiscount
    });

    return transaction;
  }

  /**
   * Calculate service total from appointment data
   */
  private static calculateServiceTotal(appointment: any): number {
    let serviceTotal = 0;

    // Add main service price
    if (typeof appointment.price === 'number') {
      serviceTotal += appointment.price;
    }

    // Add additional services prices
    if (appointment.additionalServices && appointment.additionalServices.length > 0) {
      appointment.additionalServices.forEach((service: any) => {
        if (typeof service.price === 'number') {
          serviceTotal += service.price;
        }
      });
    }

    return serviceTotal;
  }

  /**
   * Calculate product total from appointment data
   */
  private static calculateProductTotal(appointment: any): number {
    let productTotal = 0;

    // Add products prices
    if (appointment.products && appointment.products.length > 0) {
      appointment.products.forEach((product: any) => {
        if (typeof product.price === 'number') {
          productTotal += product.price;
        }
      });
    }

    return productTotal;
  }

  /**
   * Create a simple transaction from POS data
   * Records the exact final amount paid (post-discount) with service/product breakdown
   */
  static createPOSTransaction(
    posData: any,
    paymentMethod: PaymentMethod,
    finalAmount: number
  ): Transaction {
    console.log('🏗️ Creating POS transaction:', {
      paymentMethod,
      finalAmount,
      itemCount: posData.items?.length || 0
    });

    const transactionId = this.generateTransactionId();
    
    // Calculate service and product amounts from POS items
    const serviceAmount = this.calculatePOSServiceTotal(posData);
    const productAmount = this.calculatePOSProductTotal(posData);
    const originalTotal = serviceAmount + productAmount;
    
    // Calculate discount information
    const discountAmount = originalTotal - finalAmount;
    const hasDiscount = discountAmount > 0;
    
    // Create simple description
    let description = 'POS Sale';
    if (posData.items && posData.items.length > 0) {
      const serviceItems = posData.items.filter((item: any) => item.type === 'service');
      const productItems = posData.items.filter((item: any) => item.type === 'product');
      
      if (serviceItems.length > 0 && productItems.length > 0) {
        description = `POS Sale - ${serviceItems.length} service(s) and ${productItems.length} product(s)`;
      } else if (serviceItems.length > 0) {
        description = `POS Sale - ${serviceItems.length} service(s)`;
      } else {
        description = `POS Sale - ${productItems.length} product(s)`;
      }
    }
    
    // Add discount info to description if applicable
    if (hasDiscount) {
      const discountPercent = originalTotal > 0 ? ((discountAmount / originalTotal) * 100).toFixed(1) : '0';
      description += ` (${discountPercent}% discount applied)`;
    }

    // Determine transaction type based on items
    let transactionType = TransactionType.SERVICE_SALE;
    let category = "POS Sale";

    if (serviceAmount > 0 && productAmount > 0) {
      transactionType = TransactionType.CONSOLIDATED_SALE;
      category = "Consolidated Sale";
    } else if (productAmount > 0 && serviceAmount === 0) {
      transactionType = TransactionType.PRODUCT_SALE;
      category = "Product Sale";
    }

    const transaction: Transaction = {
      id: transactionId,
      date: new Date(),
      clientId: posData.clientId || 'walk-in',
      clientName: posData.clientName || 'Walk-in Customer',
      staffId: posData.staffId,
      staffName: posData.staffName,
      type: transactionType,
      category: category,
      description,
      amount: finalAmount,
      paymentMethod,
      status: TransactionStatus.COMPLETED,
      location: posData.location || "loc1",
      source: TransactionSource.POS,
      reference: {
        type: "pos",
        id: transactionId
      },
      // Add service/product breakdown
      serviceAmount: hasDiscount ? serviceAmount - (serviceAmount > 0 ? discountAmount * (serviceAmount / originalTotal) : 0) : serviceAmount,
      productAmount: hasDiscount ? productAmount - (productAmount > 0 ? discountAmount * (productAmount / originalTotal) : 0) : productAmount,
      originalServiceAmount: serviceAmount,
      discountAmount: hasDiscount ? discountAmount : undefined,
      discountPercentage: hasDiscount ? parseFloat(((discountAmount / originalTotal) * 100).toFixed(1)) : undefined,
      // Add metadata for legacy compatibility
      metadata: hasDiscount ? {
        discountApplied: true,
        originalTotal: originalTotal,
        discountAmount: discountAmount
      } : undefined,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('✅ Created POS transaction:', {
      id: transaction.id,
      type: transactionType,
      finalAmount: finalAmount,
      serviceAmount: transaction.serviceAmount,
      productAmount: transaction.productAmount,
      discountAmount: discountAmount,
      hasDiscount: hasDiscount
    });
    
    return transaction;
  }

  /**
   * Calculate service total from POS data
   */
  private static calculatePOSServiceTotal(posData: any): number {
    let serviceTotal = 0;

    if (posData.items && posData.items.length > 0) {
      posData.items.forEach((item: any) => {
        if (item.type === 'service' && typeof item.price === 'number') {
          serviceTotal += item.price * (item.quantity || 1);
        }
      });
    }

    return serviceTotal;
  }

  /**
   * Calculate product total from POS data
   */
  private static calculatePOSProductTotal(posData: any): number {
    let productTotal = 0;

    if (posData.items && posData.items.length > 0) {
      posData.items.forEach((item: any) => {
        if (item.type === 'product' && typeof item.price === 'number') {
          productTotal += item.price * (item.quantity || 1);
        }
      });
    }

    return productTotal;
  }
}
