import { Transaction, TransactionType, TransactionStatus, TransactionSource, PaymentMethod } from './transaction-types';

/**
 * Simple Transaction Service
 * Creates transactions with final amounts only - no complex discount metadata
 */
export class SimpleTransactionService {
  
  /**
   * Generate a unique transaction ID
   */
  static generateTransactionId(): string {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    return `TXN-${timestamp}-${randomSuffix}`;
  }

  /**
   * Create a simple transaction from appointment data
   * Records the exact final amount paid (post-discount)
   */
  static createAppointmentTransaction(
    appointment: any,
    paymentMethod: PaymentMethod,
    finalAmount: number
  ): Transaction {
    console.log('🏗️ Creating appointment transaction:', {
      appointmentId: appointment.id,
      paymentMethod,
      finalAmount,
      service: appointment.service
    });

    const transactionId = this.generateTransactionId();
    
    const transaction: Transaction = {
      id: transactionId,
      date: new Date(),
      clientId: appointment.clientId,
      clientName: appointment.clientName,
      staffId: appointment.staffId,
      staffName: appointment.staffName,
      type: TransactionType.SERVICE_SALE,
      category: "Service Sale",
      description: `${appointment.service} for ${appointment.clientName}`,
      amount: finalAmount,
      paymentMethod,
      status: TransactionStatus.COMPLETED,
      location: appointment.location || "loc1",
      source: TransactionSource.CALENDAR,
      reference: {
        type: "appointment",
        id: appointment.id
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('✅ Created transaction:', transaction.id, 'Amount:', finalAmount);
    return transaction;
  }

  /**
   * Create a simple transaction from POS data
   * Records the exact final amount paid (post-discount)
   */
  static createPOSTransaction(
    posData: any,
    paymentMethod: PaymentMethod,
    finalAmount: number
  ): Transaction {
    console.log('🏗️ Creating POS transaction:', {
      paymentMethod,
      finalAmount,
      itemCount: posData.items?.length || 0
    });

    const transactionId = this.generateTransactionId();
    
    // Create simple description
    let description = 'POS Sale';
    if (posData.items && posData.items.length > 0) {
      const serviceItems = posData.items.filter((item: any) => item.type === 'service');
      const productItems = posData.items.filter((item: any) => item.type === 'product');
      
      if (serviceItems.length > 0 && productItems.length > 0) {
        description = `POS Sale - ${serviceItems.length} service(s) and ${productItems.length} product(s)`;
      } else if (serviceItems.length > 0) {
        description = `POS Sale - ${serviceItems.length} service(s)`;
      } else {
        description = `POS Sale - ${productItems.length} product(s)`;
      }
    }

    const transaction: Transaction = {
      id: transactionId,
      date: new Date(),
      clientId: posData.clientId || 'walk-in',
      clientName: posData.clientName || 'Walk-in Customer',
      staffId: posData.staffId,
      staffName: posData.staffName,
      type: TransactionType.SERVICE_SALE,
      category: "POS Sale",
      description,
      amount: finalAmount,
      paymentMethod,
      status: TransactionStatus.COMPLETED,
      location: posData.location || "loc1",
      source: TransactionSource.POS,
      reference: {
        type: "pos",
        id: transactionId
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('✅ Created POS transaction:', transaction.id, 'Amount:', finalAmount);
    return transaction;
  }
}
