/**
 * Test script to verify discount calculations in POS consolidated transactions
 */

// Mock payment method enum
const PaymentMethod = {
  CASH: 'cash',
  CREDIT_CARD: 'credit_card'
};

// Mock the discount calculation logic from ConsolidatedTransactionService
const calculateDiscountedTransaction = (posData, paymentMethod, discountPercentage, discountAmount) => {
  let originalServiceAmount = 0;
  let serviceAmount = 0;
  let productAmount = 0;

  posData.items.forEach(item => {
    const itemTotal = item.price * item.quantity;

    if (item.type === 'service') {
      originalServiceAmount += itemTotal;

      // Apply discount logic
      let itemDiscountAmount = 0;
      if (discountPercentage && discountPercentage > 0) {
        itemDiscountAmount = (itemTotal * discountPercentage) / 100;
      } else if (discountAmount && discountAmount > 0) {
        // Proportional distribution of fixed discount
        const totalServiceAmount = posData.items
          .filter(i => i.type === 'service')
          .reduce((sum, i) => sum + (i.price * i.quantity), 0);
        const itemProportion = itemTotal / totalServiceAmount;
        itemDiscountAmount = discountAmount * itemProportion;
      }

      serviceAmount += itemTotal - itemDiscountAmount;
    } else {
      productAmount += itemTotal;
    }
  });

  const totalAmount = serviceAmount + productAmount;

  return {
    originalServiceAmount,
    serviceAmount,
    productAmount,
    amount: totalAmount,
    discountApplied: (discountPercentage && discountPercentage > 0) || (discountAmount && discountAmount > 0)
  };
};

console.log('🧪 Testing POS Discount Calculations...');

console.log('\n📋 Test 1: Percentage Discount on Services');
const percentageDiscountData = {
  id: 'pos-test-percentage',
  clientId: 'client-123',
  clientName: 'Test Customer',
  location: 'loc1',
  items: [
    { id: 'svc-1', name: 'Haircut', type: 'service', quantity: 1, price: 50.00 },
    { id: 'svc-2', name: 'Hair Color', type: 'service', quantity: 1, price: 80.00 },
    { id: 'prod-1', name: 'Shampoo', type: 'product', quantity: 1, price: 25.00 }
  ]
};

try {
  const percentageTransaction = calculateDiscountedTransaction(
    percentageDiscountData,
    PaymentMethod.CASH,
    15 // 15% discount
  );

  console.log(`✅ Percentage Discount Test:`);
  console.log(`- Original Service Amount: $${percentageTransaction.originalServiceAmount.toFixed(2)}`);
  console.log(`- Discounted Service Amount: $${percentageTransaction.serviceAmount.toFixed(2)}`);
  console.log(`- Product Amount: $${percentageTransaction.productAmount.toFixed(2)}`);
  console.log(`- Final Total Amount: $${percentageTransaction.amount.toFixed(2)}`);
  console.log(`- Expected Total: $${((130 * 0.85) + 25).toFixed(2)} (Services: $130 - 15% = $110.50, Products: $25)`);
  console.log(`- Discount Applied: ${percentageTransaction.discountApplied ? 'YES' : 'NO'}`);

} catch (error) {
  console.error('❌ Error testing percentage discount:', error.message);
}

console.log('\n📋 Test 2: Fixed Amount Discount on Services');
const fixedDiscountData = {
  id: 'pos-test-fixed',
  clientId: 'client-456',
  clientName: 'Test Customer 2',
  location: 'loc1',
  items: [
    { id: 'svc-1', name: 'Manicure', type: 'service', quantity: 1, price: 40.00 },
    { id: 'svc-2', name: 'Pedicure', type: 'service', quantity: 1, price: 60.00 },
    { id: 'prod-1', name: 'Nail Polish', type: 'product', quantity: 2, price: 15.00 }
  ]
};

try {
  const fixedTransaction = calculateDiscountedTransaction(
    fixedDiscountData,
    PaymentMethod.CREDIT_CARD,
    undefined, // no percentage discount
    20.00 // $20 fixed discount
  );

  console.log(`✅ Fixed Discount Test:`);
  console.log(`- Original Service Amount: $${fixedTransaction.originalServiceAmount.toFixed(2)}`);
  console.log(`- Discounted Service Amount: $${fixedTransaction.serviceAmount.toFixed(2)}`);
  console.log(`- Product Amount: $${fixedTransaction.productAmount.toFixed(2)}`);
  console.log(`- Final Total Amount: $${fixedTransaction.amount.toFixed(2)}`);
  console.log(`- Expected Total: $${(100 - 20 + 30).toFixed(2)} (Services: $100 - $20 = $80, Products: $30)`);
  console.log(`- Discount Applied: ${fixedTransaction.discountApplied ? 'YES' : 'NO'}`);

} catch (error) {
  console.error('❌ Error testing fixed discount:', error.message);
}

console.log('\n📋 Test 3: No Discount');
const noDiscountData = {
  id: 'pos-test-no-discount',
  clientId: 'client-789',
  clientName: 'Test Customer 3',
  location: 'loc1',
  items: [
    { id: 'svc-1', name: 'Facial', type: 'service', quantity: 1, price: 75.00 },
    { id: 'prod-1', name: 'Moisturizer', type: 'product', quantity: 1, price: 35.00 }
  ]
};

try {
  const noDiscountTransaction = calculateDiscountedTransaction(
    noDiscountData,
    PaymentMethod.CASH
  );

  console.log(`✅ No Discount Test:`);
  console.log(`- Service Amount: $${noDiscountTransaction.serviceAmount.toFixed(2)}`);
  console.log(`- Product Amount: $${noDiscountTransaction.productAmount.toFixed(2)}`);
  console.log(`- Final Total Amount: $${noDiscountTransaction.amount.toFixed(2)}`);
  console.log(`- Expected Total: $${(75 + 35).toFixed(2)} (No discount applied)`);
  console.log(`- Discount Applied: ${noDiscountTransaction.discountApplied ? 'YES' : 'NO'}`);

} catch (error) {
  console.error('❌ Error testing no discount:', error.message);
}

console.log('\n🎯 Summary:');
console.log('The transaction amount should always reflect the FINAL amount after discounts.');
console.log('Discounts should only be applied to service items, never to products.');
console.log('The serviceAmount and productAmount columns should show the final amounts.');
console.log('The main amount column should be serviceAmount + productAmount (final total).');
