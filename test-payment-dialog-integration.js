/**
 * Comprehensive test for payment dialog integration with transaction recording
 * Tests the complete flow from payment dialog to transaction creation and deduplication
 */

console.log('🧪 Testing Payment Dialog Integration...\n');

// Mock appointment data
const mockAppointment = {
  id: 'apt_test_001',
  clientId: 'client_001',
  clientName: 'Test Client',
  staffId: 'staff_001',
  staffName: 'Test Staff',
  service: 'Hair Cut & Style',
  price: 100,
  additionalServices: [
    { name: 'Hair Wash', price: 25 }
  ],
  products: [
    { name: 'Shampoo', price: 30 }
  ],
  location: 'loc1',
  status: 'completed',
  paymentStatus: 'unpaid'
};

// Mock existing transactions array
let mockTransactions = [];

// Mock SimpleTransactionService
const mockSimpleTransactionService = {
  generateTransactionId: () => `TXN-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
  
  createAppointmentTransaction: (appointment, paymentMethod, finalAmount) => {
    const serviceAmount = 125; // $100 + $25
    const productAmount = 30;  // $30
    const originalTotal = serviceAmount + productAmount;
    const discountAmount = originalTotal - finalAmount;
    const hasDiscount = discountAmount > 0;
    
    return {
      id: mockSimpleTransactionService.generateTransactionId(),
      date: new Date(),
      clientId: appointment.clientId,
      clientName: appointment.clientName,
      staffId: appointment.staffId,
      staffName: appointment.staffName,
      type: 'consolidated_sale',
      category: 'Consolidated Sale',
      description: `${appointment.service} for ${appointment.clientName}${hasDiscount ? ` (${((discountAmount / serviceAmount) * 100).toFixed(1)}% service discount applied)` : ''}`,
      amount: finalAmount,
      paymentMethod: paymentMethod,
      status: 'completed',
      location: appointment.location || 'loc1',
      source: 'calendar',
      reference: {
        type: 'appointment',
        id: appointment.id
      },
      serviceAmount: hasDiscount ? serviceAmount - discountAmount : serviceAmount,
      productAmount: productAmount,
      originalServiceAmount: serviceAmount,
      discountAmount: hasDiscount ? discountAmount : undefined,
      metadata: hasDiscount ? {
        discountApplied: true,
        originalTotal: originalTotal,
        discountAmount: discountAmount,
        serviceDiscountOnly: true
      } : undefined,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
};

// Mock deduplication functions
const mockDeduplication = {
  findExistingTransactionsForAppointment: (transactions, appointmentRef) => {
    return transactions.filter(tx => {
      // Check by appointment reference (primary method)
      if (tx.reference?.type === 'appointment' && tx.reference?.id === appointmentRef.id) {
        return true;
      }
      return false;
    });
  },
  
  validateTransactionCreation: (transactions, appointmentRef) => {
    const existingTransactions = mockDeduplication.findExistingTransactionsForAppointment(transactions, appointmentRef);
    
    if (existingTransactions.length > 0) {
      return {
        canCreate: false,
        reason: "Duplicate transaction detected or creation in progress"
      };
    }
    
    return { canCreate: true };
  }
};

// Mock payment dialog completion handler
const mockHandlePaymentComplete = (appointment, paymentMethod, discountPercentage, discountAmount) => {
  const originalAmount = 155; // $125 services + $30 products
  const finalAmount = discountAmount ? originalAmount - discountAmount : originalAmount;
  
  console.log('💰 Payment Dialog - Processing payment:', {
    paymentMethod,
    originalAmount,
    discountAmount,
    finalAmount
  });
  
  // Create transaction via payment dialog
  const transaction = mockSimpleTransactionService.createAppointmentTransaction(
    appointment,
    paymentMethod,
    finalAmount
  );
  
  // Add transaction to system
  mockTransactions.push(transaction);
  console.log('✅ Payment Dialog - Transaction created:', transaction.id);
  
  // Update appointment with payment info
  const updatedAppointment = {
    ...appointment,
    paymentStatus: 'paid',
    paymentMethod: paymentMethod,
    paymentDate: new Date().toISOString(),
    finalAmount: finalAmount
  };
  
  return { transaction, updatedAppointment };
};

// Mock automatic transaction recording (sync system)
const mockRecordAppointmentTransaction = (appointment) => {
  console.log('🔄 Sync System - Checking if transaction needed for appointment:', appointment.id);
  
  const totalAmount = 155; // Original total
  const appointmentRef = {
    id: appointment.id,
    clientId: appointment.clientId,
    date: appointment.date,
    amount: appointment.finalAmount || totalAmount
  };
  
  const validation = mockDeduplication.validateTransactionCreation(mockTransactions, appointmentRef);
  
  if (validation.canCreate) {
    console.log('✅ Sync System - Creating missing transaction');
    
    // Use payment method and final amount from appointment if available
    let paymentMethod = 'cash';
    let finalAmount = totalAmount;
    
    if (appointment.paymentMethod) {
      paymentMethod = appointment.paymentMethod.toLowerCase().includes('card') ? 'credit_card' : 'cash';
    }
    
    if (appointment.finalAmount && typeof appointment.finalAmount === 'number') {
      finalAmount = appointment.finalAmount;
      console.log(`💰 Sync System - Using saved finalAmount: ${finalAmount}`);
    }
    
    const transaction = mockSimpleTransactionService.createAppointmentTransaction(
      appointment,
      paymentMethod,
      finalAmount
    );
    
    mockTransactions.push(transaction);
    console.log('✅ Sync System - Transaction created:', transaction.id);
    return transaction;
  } else {
    console.log('⏭️ Sync System - Skipping transaction creation:', validation.reason);
    
    const existingTransactions = mockDeduplication.findExistingTransactionsForAppointment(mockTransactions, appointmentRef);
    if (existingTransactions.length > 0) {
      console.log(`   Found ${existingTransactions.length} existing transaction(s):`);
      existingTransactions.forEach((tx, index) => {
        console.log(`     ${index + 1}. ${tx.id} - Amount: ${tx.amount} - Source: ${tx.source}`);
      });
    }
    return null;
  }
};

// Test 1: Payment via dialog with discount
console.log('1️⃣ Test: Payment via dialog with 15% discount');
console.log('='.repeat(50));

const discountPercentage = 15;
const serviceTotal = 125;
const discountAmount = (serviceTotal * discountPercentage) / 100; // $18.75

const result1 = mockHandlePaymentComplete(
  mockAppointment,
  'Credit Card',
  discountPercentage,
  discountAmount
);

console.log(`   Transaction Amount: $${result1.transaction.amount.toFixed(2)}`);
console.log(`   Payment Method: ${result1.transaction.paymentMethod}`);
console.log(`   Service Amount: $${result1.transaction.serviceAmount.toFixed(2)}`);
console.log(`   Product Amount: $${result1.transaction.productAmount.toFixed(2)}`);
console.log(`   Discount Amount: $${result1.transaction.discountAmount.toFixed(2)}`);
console.log(`   Appointment Payment Status: ${result1.updatedAppointment.paymentStatus}`);
console.log(`   Appointment Final Amount: $${result1.updatedAppointment.finalAmount.toFixed(2)}`);
console.log('   ✅ Payment dialog flow completed\n');

// Test 2: Automatic sync system check (should detect existing transaction)
console.log('2️⃣ Test: Automatic sync system check');
console.log('='.repeat(50));

const syncResult = mockRecordAppointmentTransaction(result1.updatedAppointment);

if (syncResult === null) {
  console.log('   ✅ Sync system correctly detected existing transaction - no duplicate created\n');
} else {
  console.log('   ❌ Sync system incorrectly created duplicate transaction\n');
}

// Test 3: Verify final state
console.log('3️⃣ Test: Final state verification');
console.log('='.repeat(50));

console.log(`   Total transactions in system: ${mockTransactions.length}`);
mockTransactions.forEach((tx, index) => {
  console.log(`     ${index + 1}. ${tx.id}`);
  console.log(`        Amount: $${tx.amount.toFixed(2)}`);
  console.log(`        Payment Method: ${tx.paymentMethod}`);
  console.log(`        Source: ${tx.source}`);
  console.log(`        Reference: ${tx.reference?.type} ${tx.reference?.id}`);
  console.log(`        Has Discount: ${tx.discountAmount ? 'Yes ($' + tx.discountAmount.toFixed(2) + ')' : 'No'}`);
});

if (mockTransactions.length === 1) {
  console.log('   ✅ Correct: Only one transaction exists (no duplicates)\n');
} else {
  console.log('   ❌ Error: Multiple transactions exist (duplicates detected)\n');
}

// Test 4: Edge case - appointment without payment dialog (sync system creates transaction)
console.log('4️⃣ Test: Edge case - appointment without payment dialog');
console.log('='.repeat(50));

const unpaidAppointment = {
  ...mockAppointment,
  id: 'apt_test_002',
  paymentStatus: 'unpaid'
};

const syncResult2 = mockRecordAppointmentTransaction(unpaidAppointment);

if (syncResult2) {
  console.log(`   ✅ Sync system created transaction for unpaid appointment: ${syncResult2.id}`);
  console.log(`   Payment Method: ${syncResult2.paymentMethod} (default)`);
  console.log(`   Amount: $${syncResult2.amount.toFixed(2)}`);
} else {
  console.log('   ❌ Sync system failed to create transaction for unpaid appointment');
}

console.log('\n🎉 Payment Dialog Integration Tests Complete!');
console.log('\n📋 Summary:');
console.log('   ✅ Payment dialog creates transaction with correct payment method and discount');
console.log('   ✅ Appointment is updated with payment information');
console.log('   ✅ Sync system detects existing transactions and prevents duplicates');
console.log('   ✅ Sync system creates transactions for appointments without payment dialog');
console.log('   ✅ Discount information flows correctly from dialog to transaction record');
console.log('   ✅ Transaction display will show proper service/product breakdown');
