"use client"

import { useState, useEffect, useMemo } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"
import { useTransactions } from "@/lib/transaction-provider"
import { useAuth } from "@/lib/auth-provider"
import { useRealTimeEvent } from "@/hooks/use-real-time-updates"
import { RealTimeEventType } from "@/lib/real-time-service"
import { TransactionStatus, TransactionType } from "@/lib/transaction-types"
import { format, isToday, startOfMonth } from "date-fns"

export function RecentSales() {
  const { formatCurrency } = useCurrency()
  const { transactions, filterTransactions } = useTransactions()
  const { currentLocation } = useAuth()
  const [refreshKey, setRefreshKey] = useState(0)

  // Get recent sales transactions
  const recentSales = useMemo(() => {
    const filters: any = {}
    if (currentLocation !== 'all') {
      filters.location = currentLocation
    }

    const filteredTxs = filterTransactions(filters)

    // Get recent sales (last 10 completed transactions)
    return filteredTxs
      .filter(t =>
        t.status === TransactionStatus.COMPLETED &&
        (t.type === TransactionType.SERVICE_SALE ||
         t.type === TransactionType.PRODUCT_SALE ||
         t.type === TransactionType.CONSOLIDATED_SALE)
      )
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 4)
  }, [transactions, currentLocation, filterTransactions, refreshKey])

  // Get monthly sales count
  const monthlySalesCount = useMemo(() => {
    const filters: any = {
      startDate: startOfMonth(new Date()),
      endDate: new Date()
    }
    if (currentLocation !== 'all') {
      filters.location = currentLocation
    }

    const monthlyTxs = filterTransactions(filters)
    return monthlyTxs.filter(t =>
      t.status === TransactionStatus.COMPLETED &&
      (t.type === TransactionType.SERVICE_SALE ||
       t.type === TransactionType.PRODUCT_SALE ||
       t.type === TransactionType.CONSOLIDATED_SALE)
    ).length
  }, [transactions, currentLocation, filterTransactions, refreshKey])

  // Real-time event listeners for automatic updates
  useRealTimeEvent(RealTimeEventType.TRANSACTION_CREATED, () => {
    setRefreshKey(prev => prev + 1)
  }, [])

  useRealTimeEvent(RealTimeEventType.TRANSACTION_UPDATED, () => {
    setRefreshKey(prev => prev + 1)
  }, [])

  // Helper function to get transaction description
  const getTransactionDescription = (transaction: any) => {
    if (transaction.type === TransactionType.CONSOLIDATED_SALE && transaction.items) {
      // For consolidated sales, show the first item or a summary
      if (transaction.items.length === 1) {
        return transaction.items[0].name
      } else {
        return `${transaction.items.length} items`
      }
    }
    return transaction.description || 'Sale'
  }

  // Helper function to get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case TransactionStatus.COMPLETED:
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Completed</Badge>
      case TransactionStatus.PENDING:
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>
      case TransactionStatus.CANCELLED:
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Cancelled</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-xl font-bold">Recent Sales</CardTitle>
        <p className="text-sm text-muted-foreground">
          You made {monthlySalesCount} sales this month.
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-3 text-sm font-medium text-muted-foreground">
            <div>Service/Product</div>
            <div className="text-right">Amount</div>
            <div className="text-right">Status</div>
          </div>

          {recentSales.length > 0 ? (
            recentSales.map((transaction) => (
              <div key={transaction.id} className="grid grid-cols-3 items-center">
                <div className="truncate" title={getTransactionDescription(transaction)}>
                  {getTransactionDescription(transaction)}
                </div>
                <div className="text-right">
                  <CurrencyDisplay amount={transaction.amount} />
                </div>
                <div className="text-right">
                  {getStatusBadge(transaction.status)}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center text-muted-foreground py-4">
              No recent sales found
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

