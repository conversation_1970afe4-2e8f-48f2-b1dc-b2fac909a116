// Test script to verify real-time dashboard updates
// Run this in the browser console while viewing the dashboard

console.log("🧪 Starting Real-Time Dashboard Update Test");

// Function to create a test transaction
function createTestTransaction() {
  console.log("📝 Creating test transaction...");
  
  // Get the transaction provider from the window (if available)
  if (typeof window !== 'undefined' && window.React) {
    // Create a test transaction object
    const testTransaction = {
      id: `test-tx-${Date.now()}`,
      date: new Date(),
      clientName: "Test Client",
      staffName: "Test Staff",
      type: "SERVICE_SALE",
      category: "Service",
      description: "Test Haircut Service",
      amount: 75.00,
      paymentMethod: "CASH",
      status: "COMPLETED",
      location: "loc1",
      source: "POS",
      metadata: {
        testTransaction: true,
        createdAt: new Date().toISOString()
      }
    };

    // Try to add the transaction via localStorage (fallback method)
    try {
      const existingTransactions = JSON.parse(localStorage.getItem('vanity_transactions') || '[]');
      const updatedTransactions = [...existingTransactions, testTransaction];
      localStorage.setItem('vanity_transactions', JSON.stringify(updatedTransactions));
      
      console.log("✅ Test transaction added to localStorage:", testTransaction);
      
      // Trigger a storage event to simulate real-time update
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'vanity_transactions',
        newValue: JSON.stringify(updatedTransactions),
        oldValue: JSON.stringify(existingTransactions)
      }));
      
      // Also trigger a custom event for real-time service
      if (window.dispatchEvent) {
        window.dispatchEvent(new CustomEvent('vanity_realtime_event', {
          detail: {
            type: 'TRANSACTION_CREATED',
            payload: {
              transaction: testTransaction,
              source: testTransaction.source,
              amount: testTransaction.amount,
              clientName: testTransaction.clientName
            },
            timestamp: new Date()
          }
        }));
      }
      
      return testTransaction;
    } catch (error) {
      console.error("❌ Error creating test transaction:", error);
      return null;
    }
  } else {
    console.warn("⚠️ React not available, cannot create test transaction");
    return null;
  }
}

// Function to create a test appointment
function createTestAppointment() {
  console.log("📅 Creating test appointment...");
  
  const testAppointment = {
    id: `test-apt-${Date.now()}`,
    clientName: "Test Client",
    clientEmail: "<EMAIL>",
    service: "Test Hair Color",
    staffName: "Test Stylist",
    date: new Date(),
    duration: 90,
    location: "loc1",
    price: 120.00,
    status: "completed",
    bookingReference: `VH-${Date.now()}`,
    services: [{
      name: "Hair Color & Highlights",
      price: 120.00,
      duration: 90
    }],
    metadata: {
      testAppointment: true,
      createdAt: new Date().toISOString()
    }
  };

  try {
    const existingAppointments = JSON.parse(localStorage.getItem('vanity_appointments') || '[]');
    const updatedAppointments = [...existingAppointments, testAppointment];
    localStorage.setItem('vanity_appointments', JSON.stringify(updatedAppointments));
    
    console.log("✅ Test appointment added to localStorage:", testAppointment);
    
    // Trigger real-time event
    if (window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('vanity_realtime_event', {
        detail: {
          type: 'APPOINTMENT_CREATED',
          payload: {
            appointment: testAppointment,
            clientName: testAppointment.clientName,
            service: testAppointment.service,
            date: testAppointment.date,
            amount: testAppointment.price
          },
          timestamp: new Date()
        }
      }));
    }
    
    return testAppointment;
  } catch (error) {
    console.error("❌ Error creating test appointment:", error);
    return null;
  }
}

// Function to test real-time updates
function testRealTimeUpdates() {
  console.log("🚀 Testing real-time dashboard updates...");
  
  // Create test transaction
  const transaction = createTestTransaction();
  
  // Wait a moment, then create test appointment
  setTimeout(() => {
    const appointment = createTestAppointment();
    
    // Log results
    setTimeout(() => {
      console.log("📊 Test completed! Check the dashboard components:");
      console.log("- Recent Sales should show the new transaction");
      console.log("- Today's Appointments should show the new appointment");
      console.log("- Stats cards should update with new revenue");
      console.log("- Activity Feed should show new activities");
      
      if (transaction && appointment) {
        console.log("✅ Both test items created successfully");
      } else {
        console.log("⚠️ Some test items failed to create");
      }
    }, 1000);
  }, 500);
}

// Function to clean up test data
function cleanupTestData() {
  console.log("🧹 Cleaning up test data...");
  
  try {
    // Clean transactions
    const transactions = JSON.parse(localStorage.getItem('vanity_transactions') || '[]');
    const cleanedTransactions = transactions.filter(tx => !tx.metadata?.testTransaction);
    localStorage.setItem('vanity_transactions', JSON.stringify(cleanedTransactions));
    
    // Clean appointments
    const appointments = JSON.parse(localStorage.getItem('vanity_appointments') || '[]');
    const cleanedAppointments = appointments.filter(apt => !apt.metadata?.testAppointment);
    localStorage.setItem('vanity_appointments', JSON.stringify(cleanedAppointments));
    
    console.log("✅ Test data cleaned up");
    
    // Trigger refresh
    window.location.reload();
  } catch (error) {
    console.error("❌ Error cleaning up test data:", error);
  }
}

// Export functions to global scope for easy access
window.testRealTimeUpdates = testRealTimeUpdates;
window.createTestTransaction = createTestTransaction;
window.createTestAppointment = createTestAppointment;
window.cleanupTestData = cleanupTestData;

console.log("🎯 Real-time test functions loaded!");
console.log("Available functions:");
console.log("- testRealTimeUpdates() - Run complete test");
console.log("- createTestTransaction() - Create a test transaction");
console.log("- createTestAppointment() - Create a test appointment");
console.log("- cleanupTestData() - Remove all test data");
console.log("");
console.log("💡 Run testRealTimeUpdates() to start the test!");
